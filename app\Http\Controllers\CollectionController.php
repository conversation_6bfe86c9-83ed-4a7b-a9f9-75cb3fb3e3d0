<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\PgmServiceFile;
use App\Models\PgmServiceTown;
use App\Models\PgmXieliPerson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class CollectionController extends Controller
{
    /**
     * 获取当前用户负责的人员列表
     */
    public function list(Request $request)
    {
        try {
            $user = Auth::guard('qian')->user();
            if (!$user) {
                return response()->json(['code' => 401, 'message' => '未登录'], 401);
            }

            $town = PgmServiceTown::where('id', $user->town_id)->first();
            if (!$town) {
                return response()->json(['code' => 404, 'message' => '未找到所属乡镇'], 404);
            }

            // 获取行政村列表
            $villages = PgmXieliPerson::where('town', 'like', $town->town_abbr . '%')
                ->groupBy('big_village')
                ->pluck('big_village')
                ->filter()
                ->values()
                ->all();

            // 按乡镇和可选的 big_village 查询人员
            $query = PgmXieliPerson::where('town', 'like', $town->town_abbr . '%');

            // 过滤行政村，优先使用请求参数或用户 lbigv
            $selectedVillage = $request->big_village ?: $user->lbigv;
            if ($selectedVillage && in_array($selectedVillage, $villages)) {
                $query->where('big_village', $selectedVillage);
            } elseif (!empty($villages)) {
                $query->where('big_village', $villages[0]);
            }

            $people = $query->select([
                'id',
                'name',
                'idcard',
                'collect_time',
                'town',
                'big_village',
            ])->get();

            return response()->json([
                'code' => 200,
                'message' => '成功',
                'data' => [
                    'people' => $people,
                    'towns' => [['id' => $town->id, 'name' => $town->name]],
                    'villages' => $villages,
                    'selected_village' => $selectedVillage ?: ($villages[0] ?? null),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage()
            ], 500);
        }
    }

    // 新增方法：更新用户的 lbigv
    public function updateVillage(Request $request)
    {
        try {
            $user = Auth::guard('qian')->user();
            if (!$user) {
                return response()->json(['code' => 401, 'message' => '未登录'], 401);
            }

            $data = $request->validate([
                'big_village' => 'required|string'
            ]);

            $town = PgmServiceTown::where('id', $user->town_id)->first();
            if (!$town) {
                return response()->json(['code' => 404, 'message' => '未找到所属乡镇'], 404);
            }

            // 验证 big_village 是否有效
            $validVillages = PgmXieliPerson::where('town', 'like', $town->town_abbr . '%')
                ->groupBy('big_village')
                ->pluck('big_village')
                ->all();

            if (!in_array($data['big_village'], $validVillages)) {
                return response()->json(['code' => 400, 'message' => '无效的行政村'], 400);
            }

            $user->lbigv = $data['big_village'];
            $user->save();

            return response()->json([
                'code' => 200,
                'message' => '行政村更新成功',
                'data' => ['lbigv' => $user->lbigv]
            ]);
        } catch (\Exception $e) {
            return response()->json(['code' => 500, 'message' => '更新失败: ' . $e->getMessage()], 500);
        }
    }

    public function detail(Request $request, $id)
    {
        try {
            $person = PgmXieliPerson::find($id);
            if (!$person) {
                return response()->json(['code' => 404, 'message' => '人员不存在'], 404);
            }

            // 获取照片
            $personPhoto = PgmServiceFile::where('tablename', 'pgm_xieli_person')
                ->where('father_id', $id)
                ->where('f_type', 'zp')
                ->first();

            $caretakerPhoto = PgmServiceFile::where('tablename', 'pgm_xieli_person')
                ->where('father_id', $id)
                ->where('f_type', 'zlrzp')
                ->first();

            return response()->json([
                'code' => 200,
                'message' => '成功',
                'data' => [
                    'person' => $person,
                    'person_photo' => $personPhoto ? $personPhoto->path : null,
                    'caretaker_photo' => $caretakerPhoto ? $caretakerPhoto->path : null
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json(['code' => 500, 'message' => '服务器错误'], 500);
        }
    }

    public function save(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id' => 'required|integer|exists:pgm_xieli_person,id',
                'name' => 'required|string|max:255',
                'idcard' => 'required|string|max:18',
                'phone' => 'required|string|max:20',
                'sex' => 'required|string|in:男,女',
                'age' => 'required|integer|min:0',
                'address' => 'required|string',
                'man_state_str' => 'required|string|in:全自理,半失能,失能',
                'live_state' => 'required|string|in:本村独居,本村与照料人同住,县城内与照料人同住,德州市内与照料人同住,德州市外与照料人同住',
                'live_state_remark' => 'nullable|string|max:255|required_if:live_state,德州市外与照料人同住',
                'relation_name' => 'required|string|max:255',
                'relation_idcard' => 'required|string|max:18',
                'relation_phone' => 'required|string|max:20',
                'relation_sex' => 'required|string|in:男,女',
                'relation_age' => 'required|integer|min:0',
                'relation' => 'required|string|max:255',
                'relation_address' => 'required|string|max:255',
                'person_photo' => 'required|string',
                'caretaker_photo' => 'required|string',
            ], [
                'required' => ':attribute 为必填项',
                'integer' => ':attribute 必须为整数',
                'string' => ':attribute 必须为字符串',
                'max' => ':attribute 长度不能超过:max个字符',
                'min' => ':attribute 不能小于:min',
                'in' => ':attribute 的值不合法',
                'exists' => ':attribute 对应的记录不存在',
                'required_if' => ':attribute 为必填项（当 :other 为 :value 时）',
            ], [
                'id' => '人员ID',
                'name' => '姓名',
                'idcard' => '身份证号',
                'phone' => '电话',
                'sex' => '性别',
                'age' => '年龄',
                'address' => '地址',
                'man_state_str' => '能力等级',
                'live_state' => '居住状态',
                'live_state_remark' => '居住备注',
                'relation_name' => '照料人姓名',
                'relation_idcard' => '照料人身份证号',
                'relation_phone' => '照料人电话',
                'relation_sex' => '照料人性别',
                'relation_age' => '照料人年龄',
                'relation' => '照料人与老人关系',
                'relation_address' => '照料人地址',
                'person_photo' => '人员照片',
                'caretaker_photo' => '照料人照片',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 422,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()->all()
                ], 422);
            }

            $data = $validator->validated();

            $person = PgmXieliPerson::find($data['id']);
            if (!$person) {
                return response()->json(['code' => 404, 'message' => '人员不存在'], 404);
            }

            // 更新人员信息
            $person->update([
                'name' => $data['name'],
                'idcard' => $data['idcard'],
                'self_phone' => $data['phone'],
                'sex' => $data['sex'],
                'age' => $data['age'],
                'address' => $data['address'],
                'man_state_str' => $data['man_state_str'],
                'live_state' => $data['live_state'],
                'live_state_remark' => $data['live_state_remark'] ?? null,
                'relation_name' => $data['relation_name'],
                'relation_idcard' => $data['relation_idcard'],
                'relation_phone' => $data['relation_phone'],
                'relation_sex' => $data['relation_sex'],
                'relation_age' => $data['relation_age'],
                'relation' => $data['relation'],
                'relation_address' => $data['relation_address'],
                'collect_time' => time(),
                'cj_user_id' => Auth::guard('qian')->id(),
            ]);

            // 处理照片上传
            if (isset($data['person_photo']) && $this->isBase64($data['person_photo'])) {
                $this->savePhoto($data['person_photo'], $person->id, 'zp');
            }
            if (isset($data['caretaker_photo']) && $this->isBase64($data['caretaker_photo'])) {
                $this->savePhoto($data['caretaker_photo'], $person->id, 'zlrzp');
            }

            return response()->json(['code' => 200, 'message' => '保存成功', 'data' => $person]);

        } catch (\Exception $e) {
            return response()->json(['code' => 500, 'message' => '保存失败：' . $e->getMessage()], 500);
        }
    }

    private function savePhoto($base64String, $fatherId, $type)
    {
        $existingFile = PgmServiceFile::where('tablename', 'pgm_xieli_person')
            ->where('father_id', $fatherId)
            ->where('f_type', $type)
            ->first();

        if ($existingFile) {
            Storage::disk('public')->delete($existingFile->path);
            $existingFile->delete();
        }

        $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64String));
        $filename = $type . '_' . $fatherId . '_' . time() . '.png';
        $path = 'photos/' . $filename;
        Storage::disk('public')->put($path, $imageData);

        $md5 = md5($imageData);

        PgmServiceFile::create([
            'tablename' => 'pgm_xieli_person',
            'father_id' => $fatherId,
            'path' => $path,
            'f_type' => $type,
            'createby' => Auth::guard('qian')->id(),
            'addtime' => now(),
            't' => 0,
            'item' => null,
            'md5' => $md5,
            'is_yasuo' => false
        ]);
    }

    private function isBase64($string)
    {
        return (bool) preg_match('/^data:image\/\w+;base64,/', $string);
    }
}
