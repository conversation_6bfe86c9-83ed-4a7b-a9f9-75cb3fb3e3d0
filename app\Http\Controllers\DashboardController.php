<?php

namespace App\Http\Controllers;

use App\Models\PgmServiceTown;
use App\Models\PgmXieliOrder;
use App\Models\PgmXieliPerson;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function stats(Request $request)
    {
        try {
            $user = Auth::guard('qian')->user();
            if (!$user) {
                return response()->json(['code' => 401, 'message' => '未登录'], 401);
            }

            $town = PgmServiceTown::where('id', $user->town_id)->first();
            if (!$town) {
                return response()->json(['code' => 404, 'message' => '未找到所属乡镇'], 404);
            }

            // 获取当前月份的起止时间戳
            $startOfMonth = Carbon::now()->startOfMonth();
            $endOfMonth = Carbon::now()->endOfMonth();

            // 按乡镇和可选的 big_village 查询人员
            $query = PgmXieliPerson::where('town', 'like', $town->town_abbr . '%');

            // 1. 特困人员总数
            $totalPeople = $query->count();

            // 2. 已采集人数
            $collectedPeople = $query->whereNotNull('collect_time')->count();

            // 3. 本月已监管人数（订单有文件）
            $monitoredThisMonth = PgmXieliOrder::whereIn('person_id', $query->pluck('id'))
                ->whereBetween('add_time', [$startOfMonth, $endOfMonth])
                ->whereHas('files', function ($q) {
                    $q->where('tablename', 'pgm_xieli_order');
                })
                ->distinct('person_id')
                ->count('person_id');

            // 4. 待监管人数（本月无订单或订单无文件）
            $pendingMonitoring = $totalPeople - $monitoredThisMonth;

            return response()->json([
                'code' => 200,
                'message' => '成功',
                'data' => [
                    'total_people' => $totalPeople,
                    'collected_people' => $collectedPeople,
                    'monitored_this_month' => $monitoredThisMonth,
                    'pending_monitoring' => $pendingMonitoring,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage()
            ], 500);
        }
    }
}
