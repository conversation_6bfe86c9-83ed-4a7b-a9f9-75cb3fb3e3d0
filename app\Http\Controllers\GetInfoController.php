<?php
namespace App\Http\Controllers;

use App\Models\PgmSystemConfig;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class GetInfoController extends Controller
{
    /**
     * 获取系统配置信息
     *
     * @param Request $request
     * @return array
     */
    public function getSysConf(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'option_key' => 'required|string',
        ], [
            'option_key.required' => "参数缺失a",
        ]);

        if ($validator->fails()) {
            return [
                'msg'   => 1,
                'infor' => $validator->errors()->first(),
            ];
        }
        $keys    = explode(",", $request->option_key);
        $configs = PgmSystemConfig::whereIn('option_key', $keys)
            ->orWhere('option_key', $request->option_key)
            ->get()
            ->keyBy('option_key'); // 直接使用Eloquent的keyBy方法替代jiangwei方法

        if ($configs->isEmpty()) {
            return [
                'msg'   => 1,
                'infor' => "获取配置失败！",
            ];
        }

        // 转换配置值为数组格式
        $formattedConfigs = $configs->map(function ($item) {
            return [
                'id'                  => $item->id,
                'option_name'         => $item->option_name,
                'option_key'          => $item->option_key,
                'option_value'        => $item->option_value, // 自动转换为数组
                'option_introduction' => $item->option_introduction,
                'sort'                => $item->sort,
                'addtime'             => $item->addtime,
                'deleted'             => $item->deleted,
            ];
        });

        return [
            'msg'   => 2,
            'infor' => "获取配置成功",
            'list'  => $formattedConfigs->toArray(),
        ];
    }
}
