<?php
namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\PgmServiceUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class LoginController extends Controller
{
    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
            'role' => 'required|string'
        ]);

        $user = PgmServiceUser::where('username', $request->username)
            ->first();

        // 使用 MD5 验证密码
        if (!$user || md5($request->password) !== $user->password) {
            return response()->json([
                'code' => 401,
                'message' => '用户名或密码错误'
            ], 401);
        }

        if ($user->lock_time && now()->lessThan($user->lock_time)) {
            return response()->json([
                'code' => 403,
                'message' => '账号已被锁定，请稍后重试'
            ], 403);
        }

        $user->error_count = 0;
        $user->save();

        Auth::guard('qian')->login($user, true);

        return response()->json([
            'code' => 200,
            'message' => '登录成功',
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'truename' => $user->truename
                ]
            ]
        ]);
    }

    public function logout(Request $request)
    {
        Auth::guard('qian')->logout();
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'code' => 200,
            'message' => '退出成功'
        ]);
    }
}
