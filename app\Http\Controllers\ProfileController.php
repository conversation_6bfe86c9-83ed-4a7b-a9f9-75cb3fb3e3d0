<?php
namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProfileController extends Controller
{
    public function profile(Request $request)
    {
        try {
            $user = Auth::guard('qian')->user();
            if (!$user) {
                return response()->json(['code' => 401, 'message' => '未登录'], 401);
            }

            return response()->json([
                'code' => 200,
                'message' => '成功',
                'data' => [
                    'user' => [
                        'truename' => $user->truename,
                        'headurl' => $user->headurl,
                        'town_id' => $user->town_id,
                        'lbigv' => $user->lbigv,
                        'ltown' => $user->town->name,
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage()
            ], 500);
        }
    }

    public function logout(Request $request)
    {
        try {
            Auth::guard('qian')->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return response()->json([
                'code' => 200,
                'message' => '退出登录成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '退出登录失败: ' . $e->getMessage()
            ], 500);
        }
    }
}
