<?php
namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\PgmServiceFile;
use App\Models\PgmServiceTown;
use App\Models\PgmXieliItem;
use App\Models\PgmXieliOrder;
use App\Models\PgmXieliPerson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class RecordController extends Controller
{
    public function list(Request $request)
    {
        try {
            $user = Auth::guard('qian')->user();
            if (! $user) {
                return response()->json(['code' => 401, 'message' => '未登录'], 401);
            }

            $town = PgmServiceTown::where('id', $user->town_id)->first();
            if (! $town) {
                return response()->json(['code' => 404, 'message' => '未找到所属乡镇'], 404);
            }

            $currentMonth      = now()->format('Y-m');
            $currentMonthStart = now()->startOfMonth()->timestamp;
            $currentMonthEnd   = now()->endOfMonth()->timestamp;

            // 获取所有已采集的人员
            $query = PgmXieliPerson::where('town', 'like', $town->town_abbr . '%')
                ->whereNotNull('collect_time');

            // 获取行政村列表
            $villages = PgmXieliPerson::where('town', 'like', $town->town_abbr . '%')
                ->whereNotNull('collect_time')
                ->groupBy('big_village')
                ->pluck('big_village')
                ->filter()
                ->values()
                ->all();

            // 过滤行政村，优先使用用户的 lbigv
            $selectedVillage = $request->big_village ?: $user->lbigv;
            if ($selectedVillage && in_array($selectedVillage, $villages)) {
                $query->where('big_village', $selectedVillage);
            } elseif (! empty($villages)) {
                $query->where('big_village', $villages[0]);
            }

            $people = $query->select(['id', 'name', 'age', 'collect_time', 'relation_name', 'big_village'])
                ->with(['orders' => function ($query) use ($currentMonth, $user) {
                    $query->where('month', $currentMonth)
                                                                                  // ->where('add_uid', $user->id)
                        ->select(['id', 'person_id', 'month', 'sh_state', 'sh_text']) // 添加 sh_state 和 sh_text
                        ->with(['files' => function ($query) {
                            $query->where('tablename', 'pgm_xieli_order')
                                ->orderBy('addtime', 'desc');
                        }]);
                }])
                ->get()
                ->map(function ($person) use ($currentMonthStart, $currentMonthEnd) {
                    $order         = $person->orders->first(); // 本月订单
                    $files         = $order ? $order->files : collect();
                    $lastPhotoTime = $files->isNotEmpty() ? $files->first()->addtime : null;

                    return [
                        'id'                   => $person->id,
                        'name'                 => $person->name,
                        'age'                  => $person->age,
                        'collect_time'         => $person->collect_time,
                        'relation_name'        => $person->relation_name,
                        'big_village'          => $person->big_village,
                        'has_photos'           => $files->where('f_type', 'photo')->isNotEmpty(),
                        'has_video'            => $files->where('f_type', 'video')->isNotEmpty(),
                        'is_latest_this_month' => $person->collect_time >= $currentMonthStart && $person->collect_time <= $currentMonthEnd,
                        'last_photo_time'      => $lastPhotoTime,
                        'sh_state'             => $order ? $order->sh_state : null, // 添加 sh_state
                        'sh_text'              => $order ? $order->sh_text : null,  // 添加 sh_text
                    ];
                });

            return response()->json([
                'code'    => 200,
                'message' => '成功',
                'data'    => [
                    'records'          => $people,
                    'towns'            => [['id' => $town->id, 'name' => $town->name]],
                    'villages'         => $villages,
                    'selected_village' => $selectedVillage ?: ($villages[0] ?? null),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json(['code' => 500, 'message' => '服务器错误: ' . $e->getMessage()], 500);
        }
    }

    // 新增方法：更新用户的 lbigv
    public function updateVillage(Request $request)
    {
        try {
            $user = Auth::guard('qian')->user();
            if (! $user) {
                return response()->json(['code' => 401, 'message' => '未登录'], 401);
            }

            $data = $request->validate([
                'big_village' => 'required|string',
            ]);

            $town = PgmServiceTown::where('id', $user->town_id)->first();
            if (! $town) {
                return response()->json(['code' => 404, 'message' => '未找到所属乡镇'], 404);
            }

            // 验证 big_village 是否属于用户乡镇
            $validVillages = PgmXieliPerson::where('town', 'like', $town->town_abbr . '%')
            // ->whereNotNull('collect_time')
                ->groupBy('big_village')
                ->pluck('big_village')
                ->all();

            if (! in_array($data['big_village'], $validVillages)) {
                return response()->json(['code' => 400, 'message' => '无效的行政村'], 400);
            }

            $user->lbigv = $data['big_village'];
            $user->save();

            return response()->json([
                'code'    => 200,
                'message' => '行政村更新成功',
                'data'    => ['lbigv' => $user->lbigv],
            ]);
        } catch (\Exception $e) {
            return response()->json(['code' => 500, 'message' => '更新失败: ' . $e->getMessage()], 500);
        }
    }

    public function detail(Request $request, $id)
    {
        try {
            $user = Auth::guard('qian')->user();
            if (! $user) {
                return response()->json(['code' => 401, 'message' => '未登录'], 401);
            }

            $person = PgmXieliPerson::find($id);
            if (! $person) {
                return response()->json(['code' => 404, 'message' => '人员不存在'], 404);
            }

            $items = PgmXieliItem::select(['id', 'name'])->get();

            $currentMonth = now()->format('Y-m');
            $order        = PgmXieliOrder::where('person_id', $id)
                ->where('month', $currentMonth)
                ->first();

            if (! $order) {
                $order = PgmXieliOrder::create([
                    'person_id' => $id,
                    'month'     => $currentMonth,
                    'add_time'  => now(),
                    'add_uid'   => $user->id,
                    'context'   => '',
                ]);
            }

            $files = PgmServiceFile::where('tablename', 'pgm_xieli_order')
                ->where('father_id', $order->id)
                ->orderBy(column: 'addtime')
                ->get();

            return response()->json([
                'code'    => 200,
                'message' => '成功',
                'data'    => [
                    'record' => [
                        'id'            => $person->id,
                        'name'          => $person->name,
                        'age'           => $person->age,
                        'collect_time'  => $person->collect_time,
                        'relation_name' => $person->relation_name,
                    ],
                    'items'  => $items,
                    'files'  => $files,
                    'order'  => $order,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json(['code' => 500, 'message' => '服务器错误: ' . $e->getMessage()], 500);
        }
    }

    public function saveContext(Request $request)
    {
        try {
            $user = Auth::guard('qian')->user();
            if (! $user) {
                return response()->json(['code' => 401, 'message' => '未登录'], 401);
            }

            $data = $request->validate([
                'person_id' => 'required|integer|exists:pgm_xieli_person,id',
                'context'   => 'nullable|string',
            ]);

            $currentMonth = now()->format('Y-m');
            $order        = PgmXieliOrder::where('person_id', $data['person_id'])
                ->where('month', $currentMonth)
                ->first();

            if (! $order) {
                $order = PgmXieliOrder::create([
                    'person_id' => $data['person_id'],
                    'month'     => $currentMonth,
                    'add_time'  => now(),
                    'add_uid'   => $user->id,
                    'context'   => $data['context'] ?? '',
                ]);
            } else {
                $order->update(['context' => $data['context'] ?? '']);
            }

            return response()->json([
                'code'    => 200,
                'message' => '备注保存成功',
                'data'    => $order,
            ]);
        } catch (\Exception $e) {
            return response()->json(['code' => 500, 'message' => '保存失败: ' . $e->getMessage()], 500);
        }
    }

    // public function uploadPhoto(Request $request)
    // {
    //     try {
    //         $user = Auth::guard('qian')->user();
    //         if (! $user) {
    //             return response()->json(['code' => 401, 'message' => '未登录'], 401);
    //         }

    //         $data = $request->validate([
    //             'order_id'      => 'required|integer|exists:pgm_xieli_order,id',
    //             'item_id'       => 'required|integer|exists:pgm_xieli_item,id',
    //             'file'          => 'required|string', // base64 encoded file
    //             'type'          => 'required|in:photo,video',
    //             'collect_index' => 'required',
    //         ]);

    //         $order = PgmXieliOrder::find($data['order_id']);
    //         if (! $order) {
    //             return response()->json(['code' => 404, 'message' => '订单不存在'], 404);
    //         }

    //         // $maxT = PgmServiceFile::where('tablename', 'pgm_xieli_order')
    //         //     ->where('father_id', $data['order_id'])
    //         //     ->where('item', $data['item_id'])
    //         //     ->max('t') ?? -1;
    //         $newT = $data['collect_index'];

    //         $fileData  = base64_decode(preg_replace('#^data:(image|video)/\w+;base64,#i', '', $data['file']));
    //         $extension = $data['type'] === 'video' ? 'mp4' : 'png';
    //         $filename  = "order_{$data['order_id']}_item_{$data['item_id']}_{$newT}_" . time() . '.' . $extension;
    //         $path      = ($data['type'] === 'video' ? 'order_videos/' : 'order_photos/') . $filename;
    //         Storage::disk('public')->put($path, $fileData);

    //         $md5 = md5($fileData);

    //         $file = PgmServiceFile::create([
    //             'tablename' => 'pgm_xieli_order',
    //             'father_id' => $data['order_id'],
    //             'path'      => $path,
    //             'f_type'    => $data['type'],
    //             'createby'  => $user->id,
    //             'addtime'   => now()->timestamp,
    //             't'         => $newT,
    //             'item'      => $data['item_id'],
    //             'md5'       => $md5,
    //             'is_yasuo'  => false,
    //         ]);

    //         return response()->json([
    //             'code'    => 200,
    //             'message' => '上传成功',
    //             'data'    => ['file' => $file],
    //         ]);
    //     } catch (\Exception $e) {
    //         return response()->json(['code' => 500, 'message' => '上传失败: ' . $e->getMessage()], 500);
    //     }
    // }
    public function uploadPhoto(Request $request)
    {
        try {
            $user = Auth::guard('qian')->user();
            if (! $user) {
                return response()->json(['code' => 401, 'message' => '未登录'], 401);
            }

            $data = $request->validate([
                'order_id'      => 'required|integer|exists:pgm_xieli_order,id',
                'item_id'       => 'required|integer|exists:pgm_xieli_item,id',
                'file'          => 'required|file|mimes:jpg,jpeg,png,gif,mp4,avi,mov|max:10240000', // 100MB max
                'type'          => 'required|in:photo,video',
                'collect_index' => 'required',
            ]);

            $order = PgmXieliOrder::find($data['order_id']);
            if (! $order) {
                return response()->json(['code' => 404, 'message' => '订单不存在'], 404);
            }

            $newT         = $data['collect_index'];
            $uploadedFile = $request->file('file');

            // Validate file type against extension
            $extension              = strtolower($uploadedFile->getMimeType());
            // dd($extension);
            $allowedPhotoExtensions = ['image/jpg', 'image/jpeg', 'image/png'];
            $allowedVideoExtensions = ['video/mp4', 'video/avi', 'video/mov'];
            if ($data['type'] === 'photo' && ! in_array($extension, $allowedPhotoExtensions)) {
                return response()->json(['code' => 422, 'message' => '无效的图片格式'], 422);
            }
            if ($data['type'] === 'video' && ! in_array($extension, $allowedVideoExtensions)) {
                return response()->json(['code' => 422, 'message' => '无效的视频格式'], 422);
            }

            // Define storage path and filename
            $directory = $data['type'] === 'video' ? 'order_videos' : 'order_photos';
            $filename  = "order_{$data['order_id']}_item_{$data['item_id']}_{$newT}_" . time() . '.' . str_replace('image/','',str_replace('video/', '', $extension));
            $path      = $uploadedFile->storeAs($directory, $filename, 'public');

            // Calculate MD5 hash
            $md5 = md5_file($uploadedFile->getPathname());

            $file = PgmServiceFile::create([
                'tablename' => 'pgm_xieli_order',
                'father_id' => $data['order_id'],
                'path'      => $path,
                'f_type'    => $data['type'],
                'createby'  => $user->id,
                'addtime'   => now()->timestamp,
                't'         => $newT,
                'item'      => $data['item_id'],
                'md5'       => $md5,
                'is_yasuo'  => false,
            ]);


            return response()->json([
                'code'    => 200,
                'message' => '上传成功',
                'data'    => ['file' => $file],
            ]);
        } catch (\Exception $e) {
            return response()->json(['code' => 500, 'message' => '上传失败: ' . $e->getMessage()], 500);
        }
    }

    public function deleteFile(Request $request)
    {
        try {
            $user = Auth::guard('qian')->user();
            if (! $user) {
                return response()->json(['code' => 401, 'message' => '未登录'], 401);
            }

            $data = $request->validate([
                'file_id' => 'required|integer|exists:pgm_service_files,id',
            ]);

            $file = PgmServiceFile::where('id', $data['file_id'])
                ->where('tablename', 'pgm_xieli_order')
                ->first();

            if (! $file) {
                return response()->json(['code' => 404, 'message' => '文件不存在'], 404);
            }

            // 删除存储中的文件
            Storage::disk('public')->delete($file->path);

            // 删除数据库记录
            $file->delete();

            return response()->json([
                'code'    => 200,
                'message' => '删除成功',
            ]);
        } catch (\Exception $e) {
            return response()->json(['code' => 500, 'message' => '删除失败: ' . $e->getMessage()], 500);
        }
    }
}
