<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckIPMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // // 允许的IP地址列表
        // $allowedIps = ['*************', '***********'];

        // // 获取请求的IP地址
        // $ip = $request->ip();

        // // 检查请求的IP地址是否在允许的列表中
        // if (!in_array($ip, $allowedIps)) {
        //     return response('Unauthorized.', 401);
        // }

        return $next($request);
    }
}
