<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckRole
{
    /**
     * 检查用户角色权限
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $role 允许的角色
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $role)
    {
        // 如果用户的 role 不等于允许的  $role，返回无权限错误
        if (Auth::guard('qian')->user()->role != $role) {
            return response()->json([
                'msg' => 1,
                'infor' => '无权限访问该接口',
            ], 403);
        }

        return $next($request);
    }
}
