<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PgmServiceFile extends Model
{
    use HasFactory;

    protected $table = 'pgm_service_files';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'tablename',
        'father_id',
        'path',
        'f_type',
        'createby',
        'addtime',
        't',
        'item',
        'md5',
        'is_yasuo'
    ];

    protected $casts = [
        'is_yasuo' => 'boolean',
    ];

    /**
     * Relationship to the user who created the file
     */
    public function creator()
    {
        return $this->belongsTo(PgmServiceUser::class, 'createby');
    }

    /**
     * Relationship to the associated item (if applicable)
     */
    public function xieliItem()
    {
        return $this->belongsTo(PgmXieliItem::class, 'item');
    }

    /**
     * Scope for files of a specific table
     */
    public function scopeForTable($query, $tableName)
    {
        return $query->where('tablename', $tableName);
    }

    /**
     * Scope for files of a specific type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('f_type', $type);
    }

    /**
     * Scope for compressed files
     */
    public function scopeCompressed($query)
    {
        return $query->where('is_yasuo', true);
    }

    /**
     * Get the full URL for the file
     */
    public function getFullUrlAttribute()
    {
        return asset('storage/' . $this->path);
    }
}
