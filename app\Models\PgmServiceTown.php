<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PgmServiceTown extends Model
{
    use HasFactory;

    protected $table      = 'pgm_service_town';
    protected $primaryKey = 'id';
    public $timestamps    = false;

    protected $fillable = [
        'name',
        'town_abbr',
        'deleted',
        'sort',
    ];

    protected $casts = [
        'deleted' => 'boolean',
    ];

    /**
     * Relationship with service users associated with this town
     */
    public function serviceUsers()
    {
        return $this->hasMany(PgmServiceUser::class, 'town_id');
    }

    /**
     * Scope to include only non-deleted towns
     */
    public function scopeActive($query)
    {
        return $query->where('deleted', 0);
    }

    /**
     * Scope to order by sort field
     */
    public function scopeSorted($query)
    {
        return $query->orderBy('sort');
    }
    // 只要正常数据
    protected static function booted()
    {
        static::addGlobalScope('withoutDel', function (Builder $builder) {
            $builder->where('deleted', '0');
        });
    }
}
