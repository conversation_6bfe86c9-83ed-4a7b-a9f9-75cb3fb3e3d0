<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;

class PgmServiceUser extends Authenticatable
{
    use HasFactory;

    protected $table      = 'pgm_service_user';
    protected $primaryKey = 'id';
    public $timestamps    = false;

    protected $fillable = [
        'openid',
        'headurl',
        'username',
        'truename',
        'password',
        'error_count',
        'lock_time',
        'sex',
        'idcard',
        'phone',
        'admin_flag',
        'ent_id',
        'company_id',
        'addtime',
        'deleted',
        'area',
        'name',
        'ltown',
        'lbigv',
        'town_id',
        'wxuser',
        'sort',
        'remember_token',
    ];

    protected $casts = [
        'admin_flag' => 'integer',
        'deleted'    => 'boolean',
        'addtime'    => 'datetime',
    ];

    // Relationship to orders added by this user
    public function addedOrders()
    {
        return $this->hasMany(PgmXieliOrder::class, 'add_uid');
    }

    // Relationship to orders reviewed by this user
    public function reviewedOrders()
    {
        return $this->hasMany(PgmXieliOrder::class, 'sh_uid');
    }
    //获取用户所属的town_id的name
    public function town(): HasOne
    {
        return $this->hasOne(PgmServiceTown::class, 'id', 'town_id');
    }

    /**
     * Get the password for the user.
     *
     * @return string
     */
    public function getAuthPassword()
    {
        return $this->password;
    }
    // 只要正常数据
    protected static function booted()
    {
        static::addGlobalScope('withoutDel', function (Builder $builder) {
            $builder->where('deleted', '0')
                ->where('admin_flag', '80');
        });
    }
}
