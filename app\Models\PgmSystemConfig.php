<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PgmSystemConfig extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'pgm_system_config';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'option_name',
        'option_key',
        'option_value',
        'option_introduction',
        'sort',
        'addtime',
        'deleted'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'option_value' => 'array',
        'addtime' => 'datetime',
        'deleted' => 'boolean'
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * Get the option value as an array.
     *
     * @param  string  $value
     * @return array
     */
    public function getOptionValueAttribute($value)
    {
        return json_decode($value, true) ?? [];
    }

    /**
     * Set the option value.
     *
     * @param  array  $value
     * @return void
     */
    public function setOptionValueAttribute($value)
    {
        $this->attributes['option_value'] = json_encode($value);
    }
}
