<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PgmXieliItem extends Model
{
    use HasFactory;

    protected $table      = 'pgm_xieli_item';
    protected $primaryKey = 'id';
    public $timestamps    = false;

    protected $fillable = [
        'name',
    ];
    // 只要正常数据
    protected static function booted()
    {
        static::addGlobalScope('withoutDel', function (Builder $builder) {
            $builder->where('del_flag', '0');
        });
    }
}
