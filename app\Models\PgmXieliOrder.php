<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PgmXieliOrder extends Model
{
    use HasFactory;

    protected $table      = 'pgm_xieli_order';
    protected $primaryKey = 'id';
    public $timestamps    = false;

    protected $fillable = [
        'person_id',
        'month',
        'add_time',
        'add_uid',
        'sh_state',
        'sh_text',
        'sh_uid',
        'sh_time',
        'context'
    ];

    protected $casts = [
        'add_time' => 'datetime',
        'sh_time'  => 'datetime',
    ];

    // Relationship to the user who added the order
    public function addedBy()
    {
        return $this->belongsTo(PgmServiceUser::class, 'add_uid');
    }

    // Relationship to the user who reviewed the order
    public function reviewedBy()
    {
        return $this->belongsTo(PgmServiceUser::class, 'sh_uid');
    }
    // 只要正常数据
    protected static function booted()
    {
        static::addGlobalScope('withoutDel', function (Builder $builder) {
            $builder->where('del_flag', '0');
        });
    }
    public function files()
    {
        return $this->hasMany(PgmServiceFile::class, 'father_id')
            ->where('tablename', 'pgm_xieli_order');
    }
}
