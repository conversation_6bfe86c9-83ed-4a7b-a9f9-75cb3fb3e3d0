<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class PgmXieliPerson extends Model
{
    protected $table      = 'pgm_xieli_person';
    protected $primaryKey = 'id';
    public $incrementing  = true;
    protected $keyType    = 'int';
    public $timestamps    = false;

    protected $fillable = [
        'town',
        'big_village',
        'village',
        'address',
        'name',
        'idcard',
        'sex',
        'age',
        'relation',
        'main_name',
        'main_idcard',
        'self_phone',
        'relation_name',
        'relation_phone',
        'relation_idcard',
        'relation_sex',
        'relation_age',
        'cadre_name',
        'cadre_phone',
        'man_state_str',
        'man_state',
        'live_state',
        'ent_id',
        'company_id',
        'collect_time',
        'newlongitude',
        'newlatitude',
        'oldlongitude',
        'oldlatitude',
        'lonlat',
        'addressinfo',
        'add_time',
        'area',
        'prn1',
        'cj',
        'cjpj',
        'prt1',
        'outbj',
        'cjr',
        'cj_user_id',
        'tklb',
        'gyfs',
        'rylb',
        'quyu',
        'now_addr',
        'nowaddr_uptime',
        'addr_uptime',
        'old_addr',
        'up_time',
        'lonlat_per',
        'zjhm',
        'cjzh',
        'cjlb',
        'cjdj',
        'signal_poor',
        'live_state_remark', // 新增字段
        'relation_address', // 新增字段
    ];

    protected $casts = [
        'id'             => 'integer',
        'age'            => 'integer',
        'man_state'      => 'integer',
        'ent_id'         => 'integer',
        'company_id'     => 'integer',
        'collect_time'   => 'integer',
        'add_time'       => 'integer',
        'area'           => 'integer',
        'prn1'           => 'integer',
        'cjr'            => 'integer',
        'cj_user_id'     => 'integer',
        'quyu'           => 'integer',
        'nowaddr_uptime' => 'integer',
        'addr_uptime'    => 'integer',
        'up_time'        => 'integer',
        'signal_poor'    => 'boolean',
    ];

    protected $attributes = [
        'live_state'  => '正常',
        'signal_poor' => false,
    ];

    protected static function booted()
    {
        static::addGlobalScope('not_deleted', function (Builder $builder) {
            $builder->where('del_flag', 0)
                    ->where(function ($query) {
                        $query->whereNull('live_state')
                              ->orWhere('live_state', '!=', '取消资格');
                    });
        });
    }

    public function getAssessmentAttribute()
    {
        return $this->man_state_str ?? '未评估';
    }

    public function collector()
    {
        return $this->belongsTo(PgmServiceUser::class, 'cj_user_id');
    }

    public function scopeActive($query)
    {
        return $query->where('live_state', '正常');
    }

    public function scopeNeedAttention($query)
    {
        return $query->whereIn('live_state', ['居家', '入住养老机构'])
            ->orWhere('signal_poor', true);
    }
    public function files()
    {
        return $this->hasMany(PgmServiceFile::class, 'father_id')
            ->where('tablename', 'pgm_xieli_person');
    }
    public function orders()
    {
        return $this->hasMany(PgmXieliOrder::class, 'person_id');
    }
}
