<?php

namespace App\Services;

use Illuminate\Support\Facades\Date;

class KeyGeneratorService
{
    public function generateKey($deviceId)
    {
        // 固定盐值
        $fixedSalt = 'salst513671';

        // 获取当前日期，格式为 YYYYMMDD
        $currentDate = Date::now()->format('Ymd');

        // 拼接字符串
        $keyString = $fixedSalt . $deviceId . $currentDate;

        // 计算 MD5 哈希值
        $md5Hash = md5($keyString);

        // 返回前 8 位字符
        return substr($md5Hash, 0, 8);
    }
}
