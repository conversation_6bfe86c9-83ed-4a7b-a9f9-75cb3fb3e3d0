<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateJtLqkAIChatsTable extends Migration
{
    public function up()
    {
        Schema::create('jt_lqk_ai_chats', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('patient_id')->index(); // 患者ID
            $table->string('session_id')->index(); // 会话ID
            $table->text('message'); // 消息内容
            $table->enum('role', ['user', 'assistant']); // 角色：用户或AI
            $table->timestamp('created_at')->useCurrent();
            $table->integer('del')->default(0); // 删除标记
        });
    }

    public function down()
    {
        Schema::dropIfExists('jt_lqk_ai_chats');
    }
}
