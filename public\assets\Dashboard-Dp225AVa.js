import{c as p,o as r,b as c,d as e,t as s,n as _,j as m,F as g,r as h,u as b,g as v}from"./index-aRVed5GP.js";const x={class:"bg-white rounded-lg p-4 shadow"},f={class:"flex justify-between items-center mb-2"},y={class:"text-lg font-bold"},$={class:"text-gray-600"},w={class:"text-gray-500 text-sm mb-2"},k={class:"flex justify-end space-x-2"},j={__name:"OrderCard",props:{order:{type:Object,required:!0}},emits:["view-detail"],setup(t){const o=t,l=p(()=>({pending:"待接单",inProgress:"进行中",completed:"已完成"})[o.order.status]),u=p(()=>({pending:"text-yellow-500",inProgress:"text-blue-500",completed:"text-green-500"})[o.order.status]);return(a,n)=>(r(),c("div",x,[e("div",f,[e("h3",y,s(t.order.name),1),e("span",{class:_(u.value)},s(l.value),3)]),e("p",$,s(t.order.service)+" - "+s(t.order.duration),1),e("p",w,s(t.order.date),1),e("div",k,[e("button",{onClick:n[0]||(n[0]=i=>a.$emit("view-detail",t.order.id)),class:"bg-blue-500 text-white px-3 py-1 rounded text-sm"}," 继续服务 ")])]))}},C={class:"bg-white rounded-lg p-4 shadow"},B={class:"text-lg font-bold mb-2"},D={class:"text-gray-600 mb-2"},M={class:"text-orange-500 font-bold"},O={__name:"ServiceCard",props:{service:{type:Object,required:!0}},setup(t){return(o,l)=>(r(),c("div",C,[e("h3",B,s(t.service.name),1),e("p",D,s(t.service.description),1),e("p",M,s(t.service.price),1)]))}},P={class:"p-4"},V={class:"space-y-4"},q={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},S={__name:"Dashboard",setup(t){const o=b(),l=m([{id:1,name:"张老先生",service:"居家照护服务",duration:"2小时",status:"pending",date:"2024-01-15",phone:"13800138000",location:{longitude:"116.397428",latitude:"39.90923",label:"北京市东城区"}},{id:2,name:"李奶奶",service:"康复护理服务",duration:"3小时",status:"inProgress",date:"2024-01-16",phone:"13800138001",location:{longitude:"121.473701",latitude:"31.230416",label:"上海市黄浦区"}},{id:3,name:"王爷爷",service:"居家照护服务",duration:"4小时",status:"completed",date:"2024-01-14",phone:"13800138002",location:{longitude:"113.264385",latitude:"23.129112",label:"广州市天河区"}}]),u=m([{id:1,name:"居家照护服务",price:"100/小时",description:"提供基础生活照料、清洁卫生等服务"},{id:2,name:"康复护理服务",price:"150/小时",description:"提供专业的康复训练和护理服务"}]),a=n=>{o.push(`/staff/orders/${n}`)};return(n,i)=>(r(),c("div",P,[i[0]||(i[0]=e("h2",{class:"text-2xl font-bold mb-4"},"当前订单",-1)),e("div",V,[(r(!0),c(g,null,h(l.value,d=>(r(),v(j,{key:d.id,order:d,onViewDetail:a},null,8,["order"]))),128))]),i[1]||(i[1]=e("h2",{class:"text-2xl font-bold my-4"},"服务项目",-1)),e("div",q,[(r(!0),c(g,null,h(u.value,d=>(r(),v(O,{key:d.id,service:d},null,8,["service"]))),128))])]))}};export{S as default};
