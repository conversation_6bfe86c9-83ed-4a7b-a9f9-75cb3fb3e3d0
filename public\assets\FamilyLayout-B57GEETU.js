import{x as T,_ as H,u as A,a as k,c as m,o as n,b as l,d as a,k as F,n as h,t as g,e as p,f as I,U as v,F as b,r as y,g as x,h as _,C as $,H as N,i as R}from"./index-aRVed5GP.js";import{H as w}from"./house-CREB3yDR.js";import{C as B}from"./clipboard-list-Cx3C2T6d.js";/**
 * @license lucide-vue-next v0.471.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=T("ActivityIcon",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);/**
 * @license lucide-vue-next v0.471.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z=T("BellIcon",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]),M={class:"flex items-center justify-between"},j={__name:"FamilyHeader",setup(C){const u=A(),i=k(),d=[{name:"home",label:"首页"},{name:"records",label:"服务记录"},{name:"monitor",label:"体征监测"},{name:"alerts",label:"预警记录"},{name:"profile",label:"我的"},{name:"service",label:"服务项目介绍"},{name:"record",label:"订单详情"},{name:"alert",label:"预警详情"}],c=m(()=>{const e=JSON.parse(localStorage.getItem("user"));return(e==null?void 0:e.role)||"staff"}),o=m(()=>{const e=i.path,s=d.find(L=>e.includes(L.name));if(s)return s.label;switch(c.value){case"staff":return"服务人员端";case"family":return"家属监护端";case"volunteer":return"志愿者端";default:return"服务管理平台"}}),r=m(()=>{const e=i.path;return!d.some(s=>e.includes(s.name)&&["home","records","monitor","alerts","profile"].includes(s.name))}),t=()=>{u.push(`/${c.value}/profile`)},f=()=>{u.go(-1)};return(e,s)=>(n(),l("header",{class:h(`sticky top-0 z-50 bg-${c.value}-primary p-4 text-white shadow-md`)},[a("div",M,[r.value?(n(),l("button",{key:0,onClick:f,class:"flex items-center space-x-2 text-white hover:text-gray-200 transition-colors"},s[0]||(s[0]=[a("svg",{xmlns:"http://www.w3.org/2000/svg",class:"w-6 h-6",viewBox:"0 0 20 20",fill:"currentColor"},[a("path",{"fill-rule":"evenodd",d:"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z","clip-rule":"evenodd"})],-1)]))):F("",!0),a("h1",{class:h(["text-white text-xl font-bold",{"text-center flex-1":r.value,"text-left":!r.value}])},g(o.value),3),a("button",{class:"text-white",onClick:t},[p(I(v),{class:"w-6 h-6"})])])],2))}},V=H(j,[["__scopeId","data-v-cd4c67e4"]]),D={class:"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg px-2 py-3"},J={class:"max-w-screen-xl mx-auto"},O={class:"flex justify-around items-center"},U=["onClick"],E={class:"text-xs mt-1 font-medium"},q=["onClick"],G={class:"text-xs mt-1 font-medium"},K=["onClick"],P={class:"text-xs mt-1 font-medium"},Q={__name:"FamilyTabBar",emits:["change"],setup(C){const u=k(),i=m(()=>{const t=JSON.parse(localStorage.getItem("user"));return(t==null?void 0:t.role)||"staff"}),d=[{name:"home",label:"首页　　",icon:w},{name:"records",label:"服务记录",icon:B}],c=[{name:"alerts",label:"预警记录",icon:z},{name:"profile",label:"个人中心",icon:v}],o=m(()=>{const t={staff:[{name:"home",label:"首页",icon:w},{name:"orders",label:"订单",icon:B},{name:"schedule",label:"日程",icon:$},{name:"profile",label:"我的",icon:v}],volunteer:[{name:"home",label:"首页",icon:w},{name:"activities",label:"活动",icon:$},{name:"charity",label:"公益",icon:N},{name:"profile",label:"我的",icon:v}]};return t[i.value]||t.staff}),r=m(()=>{const t=u.path;return t.includes("/family/alert")?"alerts":t.includes("/family/record")?"records":t.includes("/family/home")?"home":t.includes("/family/records")?"records":t.includes("/family/monitor")?"monitor":t.includes("/family/alerts")?"alerts":t.includes("/family/profile")?"profile":"home"});return(t,f)=>(n(),l("nav",D,[a("div",J,[a("div",O,[i.value==="family"?(n(),l(b,{key:0},[(n(),l(b,null,y(d,e=>a("button",{key:e.name,class:h(["flex flex-col items-center px-3 py-1 rounded-lg transition-all duration-300 ease-in-out",[r.value===e.name?"bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg transform scale-110":"text-gray-500 hover:bg-gray-100"]]),onClick:s=>t.$emit("change",e.name)},[(n(),x(_(e.icon),{class:"w-6 h-6"})),a("span",E,g(e.label),1)],10,U)),64)),a("button",{class:h(["flex flex-col items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-orange-400 to-pink-500 shadow-lg transition-all duration-300 ease-in-out",[r.value==="monitor"?"transform -translate-y-6 hover:scale-110":"hover:scale-110"]]),onClick:f[0]||(f[0]=e=>t.$emit("change","monitor"))},[p(I(S),{class:"w-8 h-8 text-white"}),f[1]||(f[1]=a("span",{class:"text-xs mt-1 text-white font-medium"},"体征监测",-1))],2),(n(),l(b,null,y(c,e=>a("button",{key:e.name,class:h(["flex flex-col items-center px-3 py-1 rounded-lg transition-all duration-300 ease-in-out",[r.value===e.name?"bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg transform scale-110":"text-gray-500 hover:bg-gray-100"]]),onClick:s=>t.$emit("change",e.name)},[(n(),x(_(e.icon),{class:"w-6 h-6"})),a("span",G,g(e.label),1)],10,q)),64))],64)):(n(!0),l(b,{key:1},y(o.value,e=>(n(),l("button",{key:e.name,class:h(["flex flex-col items-center px-3 py-1 rounded-lg transition-all duration-300 ease-in-out",[r.value===e.name?"bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg transform scale-110":"text-gray-500 hover:bg-gray-100"]]),onClick:s=>t.$emit("change",e.name)},[(n(),x(_(e.icon),{class:"w-6 h-6"})),a("span",P,g(e.label),1)],10,K))),128))])])]))}},W=H(Q,[["__scopeId","data-v-8e789b16"]]),X={class:"min-h-screen bg-gray-100 pb-16"},te={__name:"FamilyLayout",setup(C){const u=k(),i=A(),d=m(()=>{const o=u.path;return o.includes("/home")?"home":o.includes("/records")?"records":o.includes("/monitor")?"monitor":o.includes("/alerts")?"alerts":o.includes("/profile")?"profile":"home"}),c=o=>{const r={home:"/family/home",records:"/family/records",monitor:"/family/monitor",alerts:"/family/alerts",profile:"/family/profile"};i.push(r[o])};return(o,r)=>{const t=R("router-view");return n(),l("div",X,[p(V),p(t),p(W,{"active-tab":d.value,onChange:c},null,8,["active-tab"])])}}};export{te as default};
