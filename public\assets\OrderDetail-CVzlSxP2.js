import{_ as q,j as c,l as z,b as a,d as l,t as d,F as w,r as x,m as f,g as E,f as m,k as N,e as V,p as h,a as L,u as T,o as s,q as C,E as j,s as $}from"./index-aRVed5GP.js";const A={class:"p-4"},M={class:"bg-white rounded-lg p-4 shadow-md"},P={class:"space-y-6"},S={class:"border-b pb-4"},G={class:"text-lg font-semibold mb-2"},H={class:"ml-4 space-y-4"},J={class:"flex overflow-x-auto space-x-4 pb-2"},K={key:0,class:"w-full h-full bg-gray-100 flex items-center justify-center text-gray-400"},Q={key:1,class:"w-full h-full relative"},W=["src"],X=["src"],Y=["onClick"],Z=["onClick"],ee=["accept","onChange"],le={class:"border-b pb-4"},te={class:"flex space-x-4"},se={class:"w-32 h-32 relative border rounded-lg overflow-hidden"},oe={key:0,class:"w-full h-full bg-gray-100 flex items-center justify-center text-gray-400"},ae={key:1,class:"w-full h-full relative"},ie=["src"],ne={class:"flex justify-end space-x-4"},re={class:"flex items-center justify-center h-full"},de=["src"],ue=["src"],ce={__name:"OrderDetail",setup(fe){const F=L(),B=T(),t=c({id:null,name:"",service:"",duration:"",status:"",date:"",requirements:[],feedbackVideo:null}),p=c(!1),v=c(""),_=c("image"),g=c("");z(()=>{const n=F.params.id,o=[{id:1,name:"张老先生",service:"居家照护服务",duration:"2小时",status:"pending",date:"2024-01-15",requirements:[{name:"按摩捶背",uploadItems:[{label:"按摩捶背第一张",type:"image",file:null},{label:"按摩捶背第二张",type:"image",file:null},{label:"按摩捶背视频",type:"video",file:null}]},{name:"洗衣服",uploadItems:[{label:"洗衣前",type:"image",file:null},{label:"洗衣后",type:"image",file:null}]}],feedbackVideo:null}].find(r=>r.id===parseInt(n));o&&(t.value=o)});const D=(n,e,o)=>{const r=n.target.files[0];if(r){const i=new FileReader;i.onload=u=>{t.value.requirements[e].uploadItems[o].file=u.target.result},i.readAsDataURL(r)}},I=n=>{const e=n.target.files[0];if(e){const o=new FileReader;o.onload=r=>{t.value.feedbackVideo=r.target.result},o.readAsDataURL(e)}},O=(n,e)=>{t.value.requirements[n].uploadItems[e].file=null},R=()=>{t.value.feedbackVideo=null},y=(n,e)=>{v.value=n,_.value=e,g.value=e==="image"?"图片预览":"视频预览",p.value=!0},U=()=>{t.value.status="completed",alert("订单已签退结束"),k()},k=()=>{B.push("/staff/orders")};return(n,e)=>(s(),a("div",A,[e[6]||(e[6]=l("h2",{class:"text-2xl font-bold mb-4"},"订单详情",-1)),l("div",M,[l("div",P,[l("div",S,[e[2]||(e[2]=l("h3",{class:"text-lg font-semibold mb-2"},"客户信息",-1)),l("p",null,"姓名: "+d(t.value.name),1),l("p",null,"服务: "+d(t.value.service),1),l("p",null,"时长: "+d(t.value.duration),1),l("p",null,"日期: "+d(t.value.date),1)]),(s(!0),a(w,null,x(t.value.requirements,(o,r)=>(s(),a("div",{key:r,class:"border-b pb-4"},[l("h3",G,"服务项目: "+d(o.name),1),l("div",H,[l("div",J,[(s(!0),a(w,null,x(o.uploadItems,(i,u)=>(s(),a("div",{key:u,class:"flex-shrink-0 w-32 h-32 relative border rounded-lg overflow-hidden"},[i.file?(s(),a("div",Q,[i.type==="image"?(s(),a("img",{key:0,src:i.file,class:"w-full h-full object-cover cursor-pointer"},null,8,W)):(s(),a("video",{key:1,src:i.file,class:"w-full h-full object-cover cursor-pointer"},null,8,X)),l("button",{class:"absolute top-1 left-1 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center z-10",onClick:f(b=>y(i.file,i.type),["stop"])}," 👁️ ",8,Y),l("button",{class:"absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center z-10",onClick:f(b=>O(r,u),["stop"])}," × ",8,Z)])):(s(),a("div",K,d(i.label),1)),l("input",{type:"file",accept:i.type==="image"?"image/*":"video/*",class:"absolute inset-0 opacity-0 cursor-pointer",onChange:b=>D(b,r,u)},null,40,ee)]))),128))])])]))),128)),l("div",le,[e[3]||(e[3]=l("h3",{class:"text-lg font-semibold mb-2"},"满意度回访视频",-1)),l("div",te,[l("div",se,[t.value.feedbackVideo?(s(),a("div",ae,[l("video",{src:t.value.feedbackVideo,class:"w-full h-full object-cover cursor-pointer"},null,8,ie),l("button",{class:"absolute top-1 left-1 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center z-10",onClick:e[0]||(e[0]=f(o=>y(t.value.feedbackVideo,"video"),["stop"]))}," 👁️ "),l("button",{class:"absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center z-10",onClick:f(R,["stop"])}," × ")])):(s(),a("div",oe," 上传回访视频 ")),l("input",{type:"file",accept:"video/*",class:"absolute inset-0 opacity-0 cursor-pointer",onChange:I},null,32)])])]),l("div",ne,[t.value.status==="inProgress"?(s(),E(m(j),{key:0,type:"success",onClick:U},{default:h(()=>e[4]||(e[4]=[C(" 签退结束 ")])),_:1})):N("",!0),V(m(j),{type:"info",onClick:k},{default:h(()=>e[5]||(e[5]=[C(" 返回 ")])),_:1})])])]),V(m($),{modelValue:p.value,"onUpdate:modelValue":e[1]||(e[1]=o=>p.value=o),title:g.value,fullscreen:"","close-on-click-modal":!1},{default:h(()=>[l("div",re,[_.value==="image"?(s(),a("img",{key:0,src:v.value,class:"max-w-full max-h-full"},null,8,de)):(s(),a("video",{key:1,src:v.value,controls:"",class:"max-w-full max-h-full"},null,8,ue))])]),_:1},8,["modelValue","title"])]))}},ve=q(ce,[["__scopeId","data-v-76050164"]]);export{ve as default};
