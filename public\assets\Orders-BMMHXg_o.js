import{c as v,o as u,b as c,d as e,t as l,n as f,k as g,j as x,w as h,v as y,F as $,r as w,u as k,g as C}from"./index-aRVed5GP.js";const O={class:"bg-white rounded-lg p-4 shadow"},P={class:"flex justify-between items-center mb-2"},M={class:"text-lg font-bold"},j={class:"text-gray-600"},B={class:"text-gray-500 text-sm mb-2"},V={class:"flex items-center space-x-2 mb-2"},D=["href"],S={class:"flex items-center space-x-2 mb-4"},F=["href"],L={class:"flex justify-end space-x-2"},N={__name:"OrderListItem",props:{order:{type:Object,required:!0}},emits:["accept","complete","view"],setup(t){const m=t,d=v(()=>({pending:"待接单",inProgress:"进行中",completed:"已完成"})[m.order.status]),i=v(()=>({pending:"text-yellow-500",inProgress:"text-blue-500",completed:"text-green-500"})[m.order.status]);return(n,s)=>(u(),c("div",O,[e("div",P,[e("h3",M,l(t.order.name),1),e("span",{class:f(i.value)},l(d.value),3)]),e("p",j,l(t.order.service)+" - "+l(t.order.duration),1),e("p",B,l(t.order.date),1),e("div",V,[s[3]||(s[3]=e("span",{class:"text-gray-600"},"电话：",-1)),e("a",{href:`tel:${t.order.phone}`,class:"text-blue-500 hover:text-blue-600"},l(t.order.phone),9,D)]),e("div",S,[s[4]||(s[4]=e("span",{class:"text-gray-600"},"地址：",-1)),e("a",{href:`https://uri.amap.com/marker?position=${t.order.location.longitude},${t.order.location.latitude}&name=${t.order.location.label}`,target:"_blank",class:"text-blue-500 hover:text-blue-600"},l(t.order.location.label),9,F)]),e("div",L,[t.order.status==="pending"?(u(),c("button",{key:0,onClick:s[0]||(s[0]=p=>n.$emit("accept",t.order.id)),class:"bg-green-500 text-white px-3 py-1 rounded text-sm"}," 接单 ")):g("",!0),t.order.status==="inProgress"?(u(),c("button",{key:1,onClick:s[1]||(s[1]=p=>n.$emit("complete",t.order.id)),class:"bg-blue-500 text-white px-3 py-1 rounded text-sm"}," 完成服务 ")):g("",!0),e("button",{onClick:s[2]||(s[2]=p=>n.$emit("view",t.order.id)),class:"bg-gray-500 text-white px-3 py-1 rounded text-sm"}," 查看详情 ")])]))}},q={class:"p-4"},z={class:"mb-4"},A={class:"space-y-4"},I={__name:"Orders",setup(t){const m=k(),d=x("all"),i=x([{id:1,name:"张老先生",service:"居家照护服务",duration:"2小时",status:"pending",date:"2024-01-15",phone:"13800138000",location:{longitude:"116.397428",latitude:"39.90923",label:"北京市东城区"}},{id:2,name:"李奶奶",service:"康复护理服务",duration:"3小时",status:"inProgress",date:"2024-01-16",phone:"13800138001",location:{longitude:"121.473701",latitude:"31.230416",label:"上海市黄浦区"}},{id:3,name:"王爷爷",service:"居家照护服务",duration:"4小时",status:"completed",date:"2024-01-14",phone:"13800138002",location:{longitude:"113.264385",latitude:"23.129112",label:"广州市天河区"}}]),n=v(()=>d.value==="all"?i.value:i.value.filter(r=>r.status===d.value)),s=r=>{const o=i.value.find(a=>a.id===r);o&&(o.status="inProgress")},p=r=>{const o=i.value.find(a=>a.id===r);o&&(o.status="completed")},b=r=>{m.push(`/staff/orders/${r}`)};return(r,o)=>(u(),c("div",q,[o[2]||(o[2]=e("h2",{class:"text-2xl font-bold mb-4"},"订单管理",-1)),e("div",z,[h(e("select",{"onUpdate:modelValue":o[0]||(o[0]=a=>d.value=a),class:"w-full p-2 border rounded-md"},o[1]||(o[1]=[e("option",{value:"all"},"所有订单",-1),e("option",{value:"pending"},"待接单",-1),e("option",{value:"inProgress"},"进行中",-1),e("option",{value:"completed"},"已完成",-1)]),512),[[y,d.value]])]),e("div",A,[(u(!0),c($,null,w(n.value,a=>(u(),C(N,{key:a.id,order:a,onAccept:s,onComplete:p,onView:b},null,8,["order"]))),128))])]))}};export{I as default};
