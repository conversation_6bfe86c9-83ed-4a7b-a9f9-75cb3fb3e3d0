import{j as a,b as i,d as s,e as u,f as p,t as l,q as o,u as f,o as m,U as b}from"./index-aRVed5GP.js";const g={class:"p-4"},v={class:"bg-white rounded-lg p-4 shadow mb-4"},c={class:"flex items-center space-x-4"},x={class:"w-20 h-20 bg-orange-200 rounded-full flex items-center justify-center"},h={class:"text-xl font-bold"},w={class:"text-gray-500"},y={class:"bg-white rounded-lg p-4 shadow mb-4"},S={class:"bg-white rounded-lg p-4 shadow mb-4"},N={__name:"Profile",setup(_){const d=f(),e=a({name:"李明",role:"高级护理员",id:"ST12345",phone:"13800138000",email:"<EMAIL>"}),n=a({monthlyServices:28,monthlyHours:84,totalServices:356,averageRating:4.8}),r=()=>{d.push("/login?role=staff")};return(k,t)=>(m(),i("div",g,[s("div",v,[s("div",c,[s("div",x,[u(p(b),{class:"w-12 h-12 text-orange-500"})]),s("div",null,[s("h2",h,l(e.value.name),1),s("p",w,l(e.value.role),1)])])]),s("div",y,[t[3]||(t[3]=s("h3",{class:"text-lg font-bold mb-2"},"基本信息",-1)),s("p",null,[t[0]||(t[0]=s("span",{class:"font-bold"},"ID:",-1)),o(" "+l(e.value.id),1)]),s("p",null,[t[1]||(t[1]=s("span",{class:"font-bold"},"联系电话:",-1)),o(" "+l(e.value.phone),1)]),s("p",null,[t[2]||(t[2]=s("span",{class:"font-bold"},"邮箱:",-1)),o(" "+l(e.value.email),1)])]),s("div",S,[t[8]||(t[8]=s("h3",{class:"text-lg font-bold mb-2"},"工作统计",-1)),s("p",null,[t[4]||(t[4]=s("span",{class:"font-bold"},"本月服务次数:",-1)),o(" "+l(n.value.monthlyServices),1)]),s("p",null,[t[5]||(t[5]=s("span",{class:"font-bold"},"本月服务时长:",-1)),o(" "+l(n.value.monthlyHours)+" 小时",1)]),s("p",null,[t[6]||(t[6]=s("span",{class:"font-bold"},"总服务次数:",-1)),o(" "+l(n.value.totalServices),1)]),s("p",null,[t[7]||(t[7]=s("span",{class:"font-bold"},"平均评分:",-1)),o(" "+l(n.value.averageRating),1)])]),s("button",{onClick:r,class:"w-full bg-orange-500 text-white py-2 px-4 rounded-md mt-4"}," 退出登录 ")]))}};export{N as default};
