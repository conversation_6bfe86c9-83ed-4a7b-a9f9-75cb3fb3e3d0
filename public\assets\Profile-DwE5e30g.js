import{j as a,b as r,d as s,e as u,f as p,t as l,q as o,u as m,o as f,U as b}from"./index-aRVed5GP.js";const g={class:"p-4"},x={class:"bg-white rounded-lg p-4 shadow mb-4"},h={class:"flex items-center space-x-4"},v={class:"w-20 h-20 bg-orange-200 rounded-full flex items-center justify-center"},c={class:"text-xl font-bold"},w={class:"bg-white rounded-lg p-4 shadow mb-4"},y={class:"bg-white rounded-lg p-4 shadow mb-4"},B={__name:"Profile",setup(k){const d=m(),e=a({name:"张小明",relationship:"儿子",phone:"13900139000",email:"<EMAIL>"}),n=a({monthlyChecks:45,monthlyAlerts:3,lastVisit:"2024-01-07 18:30"}),i=()=>{d.push("/login?role=family")};return(V,t)=>(f(),r("div",g,[s("div",x,[s("div",h,[s("div",v,[u(p(b),{class:"w-12 h-12 text-orange-500"})]),s("div",null,[s("h2",c,l(e.value.name),1),t[0]||(t[0]=s("p",{class:"text-gray-500"},"家属",-1))])])]),s("div",w,[t[4]||(t[4]=s("h3",{class:"text-lg font-bold mb-2"},"基本信息",-1)),s("p",null,[t[1]||(t[1]=s("span",{class:"font-bold"},"关系:",-1)),o(" "+l(e.value.relationship),1)]),s("p",null,[t[2]||(t[2]=s("span",{class:"font-bold"},"联系电话:",-1)),o(" "+l(e.value.phone),1)]),s("p",null,[t[3]||(t[3]=s("span",{class:"font-bold"},"邮箱:",-1)),o(" "+l(e.value.email),1)])]),s("div",y,[t[8]||(t[8]=s("h3",{class:"text-lg font-bold mb-2"},"监护统计",-1)),s("p",null,[t[5]||(t[5]=s("span",{class:"font-bold"},"本月查看次数:",-1)),o(" "+l(n.value.monthlyChecks),1)]),s("p",null,[t[6]||(t[6]=s("span",{class:"font-bold"},"本月处理预警:",-1)),o(" "+l(n.value.monthlyAlerts),1)]),s("p",null,[t[7]||(t[7]=s("span",{class:"font-bold"},"上次访问时间:",-1)),o(" "+l(n.value.lastVisit),1)])]),s("button",{onClick:i,class:"w-full bg-orange-500 text-white py-2 px-4 rounded-md mt-4"}," 退出登录 ")]))}};export{B as default};
