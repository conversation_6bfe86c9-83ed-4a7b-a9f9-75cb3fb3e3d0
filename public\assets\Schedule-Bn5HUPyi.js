import{o as s,b as o,d as e,t as i,n as b,F as p,r as D,k as g,c as M,e as _,j as w,g as N}from"./index-aRVed5GP.js";const O={key:0},q={class:"list-disc list-inside"},x={__name:"OrderList",props:{title:{type:String,required:!0},orders:{type:Number,required:!0},color:{type:String,required:!0}},setup(n){return(t,c)=>n.orders>0?(s(),o("div",O,[e("h4",{class:b(["font-bold mb-2",`text-${n.color}-500`])},i(n.title)+" ("+i(n.orders)+")",3),e("ul",q,[(s(!0),o(p,null,D(n.orders,u=>(s(),o("li",{key:u,class:"text-gray-600"}," 模拟订单 #"+i(Math.floor(Math.random()*1e3)+1),1))),128))])])):g("",!0)}},B={class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},L={class:"bg-white rounded-lg p-6 w-full max-w-md"},P={class:"text-xl font-bold mb-4"},Y={key:0,class:"space-y-4"},z={key:1,class:"text-center text-gray-500"},V={__name:"OrderDetailsModal",props:{day:{type:Object,required:!0}},emits:["close"],setup(n){const t=n,c=M(()=>t.day.orders.pending>0||t.day.orders.inProgress>0||t.day.orders.completed>0);return(u,m)=>(s(),o("div",B,[e("div",L,[e("h3",P,i(n.day.date.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"}))+" 订单详情 ",1),c.value?(s(),o("div",Y,[_(x,{title:"待开始",orders:n.day.orders.pending,color:"yellow"},null,8,["orders"]),_(x,{title:"进行中",orders:n.day.orders.inProgress,color:"red"},null,8,["orders"]),_(x,{title:"已完成",orders:n.day.orders.completed,color:"green"},null,8,["orders"])])):(s(),o("div",z," 该日期没有订单 ")),e("button",{onClick:m[0]||(m[0]=k=>u.$emit("close")),class:"mt-6 w-full bg-orange-500 text-white py-2 px-4 rounded-md hover:bg-orange-600 transition duration-200"}," 关闭 ")])]))}},E={class:"p-4"},R={class:"mb-4 flex justify-between items-center"},A={class:"text-xl font-bold"},G={class:"grid grid-cols-7 gap-2 mb-2"},H={class:"grid grid-cols-7 gap-2"},I=["onClick"],J={class:"flex justify-center mt-2 space-x-1"},K={key:0,class:"w-2 h-2 rounded-full bg-yellow-500"},Q={key:1,class:"w-2 h-2 rounded-full bg-red-500"},T={key:2,class:"w-2 h-2 rounded-full bg-green-500"},W={__name:"Schedule",setup(n){const t=w(new Date),c=w(null),u=["日","一","二","三","四","五","六"],m=M(()=>t.value.toLocaleString("default",{year:"numeric",month:"long"})),k=M(()=>{const d=t.value.getFullYear(),l=t.value.getMonth(),r=new Date(d,l,1),f=new Date(d,l+1,0),y=[],j=r.getDay(),F=6-f.getDay();for(let a=j-1;a>=0;a--){const h=new Date(d,l,-a);y.push({date:h,isCurrentMonth:!1,orders:v()})}for(let a=1;a<=f.getDate();a++){const h=new Date(d,l,a);y.push({date:h,isCurrentMonth:!0,orders:v()})}for(let a=1;a<=F;a++){const h=new Date(d,l+1,a);y.push({date:h,isCurrentMonth:!1,orders:v()})}return y});function v(){return{pending:Math.random()<.3?Math.floor(Math.random()*3)+1:0,inProgress:Math.random()<.3?Math.floor(Math.random()*3)+1:0,completed:Math.random()<.3?Math.floor(Math.random()*3)+1:0}}const C=()=>{t.value=new Date(t.value.getFullYear(),t.value.getMonth()-1,1)},$=()=>{t.value=new Date(t.value.getFullYear(),t.value.getMonth()+1,1)},S=d=>{c.value=d};return(d,l)=>(s(),o("div",E,[l[1]||(l[1]=e("h2",{class:"text-2xl font-bold mb-4"},"日程安排",-1)),e("div",R,[e("button",{onClick:C,class:"text-orange-500"},"< 上个月"),e("h3",A,i(m.value),1),e("button",{onClick:$,class:"text-orange-500"},"下个月 >")]),e("div",G,[(s(),o(p,null,D(u,r=>e("div",{key:r,class:"text-center font-bold text-gray-500"},i(r),1)),64))]),e("div",H,[(s(!0),o(p,null,D(k.value,r=>(s(),o("div",{key:r.date,class:b(["p-2 border rounded-md text-center h-24 relative",{"bg-gray-100":!r.isCurrentMonth}]),onClick:f=>S(r)},[e("p",{class:b({"text-gray-400":!r.isCurrentMonth})},i(r.date.getDate()),3),e("div",J,[r.orders.pending?(s(),o("div",K)):g("",!0),r.orders.inProgress?(s(),o("div",Q)):g("",!0),r.orders.completed?(s(),o("div",T)):g("",!0)])],10,I))),128))]),c.value?(s(),N(V,{key:0,day:c.value,onClose:l[0]||(l[0]=r=>c.value=null)},null,8,["day"])):g("",!0)]))}};export{W as default};
