import{u as f,o as n,b as i,d as s,e as c,f as m,U as d,F as p,r as _,n as b,g as x,h as g,t as v,C,c as y,a as w,i as k}from"./index-aRVed5GP.js";import{H as T}from"./house-CREB3yDR.js";import{C as $}from"./clipboard-list-Cx3C2T6d.js";const B={class:"sticky top-0 z-10 bg-[#FFA500] p-4 text-white shadow-md"},S={class:"flex items-center justify-between"},F={__name:"StaffHeader",setup(r){const a=f(),o=()=>{a.push("/staff/profile")};return(l,e)=>(n(),i("header",B,[s("div",S,[e[0]||(e[0]=s("h1",{class:"text-white text-xl font-bold"},"服务人员工作台",-1)),s("button",{class:"text-white",onClick:o},[c(m(d),{class:"w-6 h-6"})])])]))}},H={class:"fixed bottom-0 left-0 right-0 bg-white border-t"},L={class:"flex justify-around py-2"},j=["onClick"],z={class:"text-xs mt-1"},D={__name:"StaffTabBar",props:{activeTab:{type:String,required:!0}},emits:["change"],setup(r){const a=[{name:"home",label:"首页",icon:T},{name:"orders",label:"订单",icon:$},{name:"schedule",label:"日程",icon:C},{name:"profile",label:"我的",icon:d}];return(o,l)=>(n(),i("nav",H,[s("div",L,[(n(),i(p,null,_(a,e=>s("button",{key:e.name,class:b(["flex flex-col items-center px-4",{"text-orange-500":r.activeTab===e.name,"text-gray-400":r.activeTab!==e.name}]),onClick:t=>o.$emit("change",e.name)},[(n(),x(g(e.icon),{class:"w-6 h-6"})),s("span",z,v(e.label),1)],10,j)),64))])]))}},N={class:"min-h-screen bg-gray-100 pb-16"},q={__name:"StaffLayout",setup(r){const a=w(),o=f(),l=y(()=>{const t=a.path;return t.includes("/orders")?"orders":t.includes("/schedule")?"schedule":t.includes("/profile")?"profile":"home"}),e=t=>{const u={home:"/staff/home",orders:"/staff/orders",schedule:"/staff/schedule",profile:"/staff/profile"};o.push(u[t])};return(t,u)=>{const h=k("router-view");return n(),i("div",N,[c(F),c(h),c(D,{"active-tab":l.value,onChange:e},null,8,["active-tab"])])}}};export{q as default};
