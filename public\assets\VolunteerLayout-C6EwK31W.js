import{u as _,a as d,c as p,o as r,b as h,d as n,t as f,e as u,f as g,U as v,n as b,F as x,r as w,g as C,h as $,C as k,H as T,i as B}from"./index-aRVed5GP.js";import{H}from"./house-CREB3yDR.js";const V={class:"flex items-center justify-between"},S={class:"text-white text-xl font-bold"},N={__name:"VolunteerHeader",setup(c){const s=_(),o=d(),i=[{name:"home",label:"首页"},{name:"activities",label:"活动"},{name:"charity",label:"公益"},{name:"profile",label:"我的"}],e=p(()=>{const a=JSON.parse(localStorage.getItem("user"));return(a==null?void 0:a.role)||"volunteer"}),t=p(()=>{const a=o.path,m=i.find(y=>a.includes(y.name));if(m)return m.label;switch(e.value){case"staff":return"服务人员端";case"family":return"家属监护端";case"volunteer":return"志愿者端";default:return"服务管理平台"}}),l=()=>{s.push(`/${e.value}/profile`)};return(a,m)=>(r(),h("header",{class:b(`sticky top-0 z-10 bg-${e.value}-primary p-4 text-white shadow-md`)},[n("div",V,[n("h1",S,f(t.value),1),n("button",{class:"text-white",onClick:l},[u(g(v),{class:"w-6 h-6"})])])],2))}},R={class:"fixed bottom-0 left-0 right-0 bg-white border-t"},j={class:"flex justify-around py-2"},z=["onClick"],D={class:"text-xs mt-1"},F={__name:"VolunteerTabBar",props:{activeTab:{type:String,required:!0}},emits:["change"],setup(c){const s=[{name:"home",label:"首页",icon:H},{name:"activities",label:"活动",icon:k},{name:"charity",label:"公益",icon:T},{name:"profile",label:"我的",icon:v}];return(o,i)=>(r(),h("nav",R,[n("div",j,[(r(),h(x,null,w(s,e=>n("button",{key:e.name,class:b(["flex flex-col items-center px-4",{"text-orange-500":c.activeTab===e.name,"text-gray-400":c.activeTab!==e.name}]),onClick:t=>o.$emit("change",e.name)},[(r(),C($(e.icon),{class:"w-6 h-6"})),n("span",D,f(e.label),1)],10,z)),64))])]))}},L={class:"min-h-screen bg-gray-100 pb-16"},E={__name:"VolunteerLayout",setup(c){const s=d(),o=_(),i=p(()=>{const t=s.path;return t.includes("/activities")?"activities":t.includes("/charity")?"charity":t.includes("/profile")?"profile":"home"}),e=t=>{const l={home:"/volunteer/home",activities:"/volunteer/activities",charity:"/volunteer/charity",profile:"/volunteer/profile"};o.push(l[t])};return(t,l)=>{const a=B("router-view");return r(),h("div",L,[u(N),u(a),u(F,{"active-tab":i.value,onChange:e},null,8,["active-tab"])])}}};export{E as default};
