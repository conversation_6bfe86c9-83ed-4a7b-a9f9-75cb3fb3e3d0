<?php

use App\Http\Controllers\CollectionController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\GetInfoController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RecordController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/api/error', function () {
    $list['msg']       = "登录信息已失效，请重新登录";
    $list['errorCode'] = 401;
    $list['infor']     = "登录信息已失效，请重新登录";
    return $list;
})->name("login");

// 每个客户端IP每分钟最大请求30次 index路由
// ->middleware('throttle:120:1')
Route::prefix('api')->middleware('checkip')->group(function () {
    Route::post('/login', [LoginController::class, 'login']);
    Route::middleware('auth:qian')->post('/logout', [LoginController::class, 'logout']);
    Route::middleware('auth:qian')->group(function () {
        Route::get('/dashboard/stats', [DashboardController::class, 'stats']); // 新增
        Route::get('/collection/list', [CollectionController::class, 'list']);
        Route::get('/collection/detail/{id}', [CollectionController::class, 'detail']);
        Route::post('/collection/save', [CollectionController::class, 'save']);

        Route::get('/records/list', [RecordController::class, 'list']);
        Route::get('/records/detail/{id}', [RecordController::class, 'detail']);
        Route::post('/records/save-context', [RecordController::class, 'saveContext']);
        Route::post('/records/upload-photo', [RecordController::class, 'uploadPhoto']);
        Route::post('/records/delete-file', [RecordController::class, 'deleteFile']);
        Route::post('/records/update-village', [RecordController::class, 'updateVillage']); // 新增
        Route::get('/profile', [ProfileController::class, 'profile']);                      // 新增
        Route::post('/logout', [ProfileController::class, 'logout']);                       // 新增
        Route::get('/sysconfig', [GetInfoController::class, 'getSysConf']); // 新增
    });
});
